import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_response.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8080/api/v1';
  static const String authTokenKey = 'auth_token';
  
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();
  
  ApiService._();
  
  String? _authToken;
  
  // Get auth token from storage
  Future<String?> getAuthToken() async {
    if (_authToken != null) return _authToken;
    
    final prefs = await SharedPreferences.getInstance();
    _authToken = prefs.getString(authTokenKey);
    return _authToken;
  }
  
  // Save auth token to storage
  Future<void> saveAuthToken(String token) async {
    _authToken = token;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(authTokenKey, token);
  }
  
  // Clear auth token
  Future<void> clearAuthToken() async {
    _authToken = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(authTokenKey);
  }
  
  // Get headers with auth token
  Future<Map<String, String>> _getHeaders({bool includeAuth = true}) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    if (includeAuth) {
      final token = await getAuthToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }
    
    return headers;
  }
  
  // Generic GET request
  Future<http.Response> get(String endpoint, {Map<String, String>? queryParams}) async {
    final uri = Uri.parse('$baseUrl$endpoint');
    final uriWithQuery = queryParams != null 
        ? uri.replace(queryParameters: queryParams)
        : uri;
    
    final headers = await _getHeaders();
    return await http.get(uriWithQuery, headers: headers);
  }
  
  // Generic POST request
  Future<http.Response> post(String endpoint, {Map<String, dynamic>? body, bool includeAuth = true}) async {
    final uri = Uri.parse('$baseUrl$endpoint');
    final headers = await _getHeaders(includeAuth: includeAuth);
    
    return await http.post(
      uri,
      headers: headers,
      body: body != null ? jsonEncode(body) : null,
    );
  }
  
  // Generic PUT request
  Future<http.Response> put(String endpoint, {Map<String, dynamic>? body}) async {
    final uri = Uri.parse('$baseUrl$endpoint');
    final headers = await _getHeaders();
    
    return await http.put(
      uri,
      headers: headers,
      body: body != null ? jsonEncode(body) : null,
    );
  }
  
  // Generic DELETE request
  Future<http.Response> delete(String endpoint) async {
    final uri = Uri.parse('$baseUrl$endpoint');
    final headers = await _getHeaders();
    
    return await http.delete(uri, headers: headers);
  }
  
  // Handle API response
  T handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      final Map<String, dynamic> data = jsonDecode(response.body);
      return fromJson(data);
    } else {
      final Map<String, dynamic> errorData = jsonDecode(response.body);
      final errorInfo = ErrorInfo.fromJson(errorData['error'] ?? {});
      throw ApiException(
        statusCode: response.statusCode,
        error: errorInfo,
      );
    }
  }
  
  // Handle list response
  List<T> handleListResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      final Map<String, dynamic> responseData = jsonDecode(response.body);
      final List<dynamic> data = responseData['data'] ?? [];
      return data.map((item) => fromJson(item as Map<String, dynamic>)).toList();
    } else {
      final Map<String, dynamic> errorData = jsonDecode(response.body);
      final errorInfo = ErrorInfo.fromJson(errorData['error'] ?? {});
      throw ApiException(
        statusCode: response.statusCode,
        error: errorInfo,
      );
    }
  }
}

class ApiException implements Exception {
  final int statusCode;
  final ErrorInfo error;
  
  ApiException({
    required this.statusCode,
    required this.error,
  });
  
  @override
  String toString() {
    return 'ApiException: ${error.message} (${error.code})';
  }
}
