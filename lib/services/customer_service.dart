import 'dart:convert';
import '../models/customer.dart';
import '../models/api_response.dart';
import 'api_service.dart';

class CustomerService {
  final ApiService _apiService = ApiService.instance;
  
  static CustomerService? _instance;
  static CustomerService get instance => _instance ??= CustomerService._();
  
  CustomerService._();
  
  // Get customers with pagination
  Future<PaginatedCustomersResult> getCustomers({
    int page = 1,
    int perPage = 10,
    String? search,
    String? name,
    String? phone,
  }) async {
    print('👥 Getting customers - page: $page, search: "$search"');
    final queryParams = <String, String>{
      'page': page.toString(),
      'per_page': perPage.toString(),
    };

    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }
    if (name != null && name.isNotEmpty) {
      queryParams['name'] = name;
    }
    if (phone != null && phone.isNotEmpty) {
      queryParams['phone'] = phone;
    }

    final response = await _apiService.get('/customers', queryParams: queryParams);
    print('👥 Customers response status: ${response.statusCode}');
    print('👥 Customers response body: ${response.body}');

    try {
      // Manual parsing to avoid type casting issues
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('🔍 Parsing response body...');
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print('🔍 Response data keys: ${responseData.keys}');
        print('🔍 Data type: ${responseData['data'].runtimeType}');
        print('🔍 Pagination type: ${responseData['pagination'].runtimeType}');

        final List<dynamic> dataArray = responseData['data'] as List<dynamic>;
        final Map<String, dynamic> paginationData = responseData['pagination'] as Map<String, dynamic>;

        final customers = <Customer>[];
        for (int i = 0; i < dataArray.length; i++) {
          try {
            final item = dataArray[i] as Map<String, dynamic>;
            print('🔍 Parsing customer $i: $item');
            final customer = Customer.fromJson(item);
            customers.add(customer);
          } catch (e) {
            print('❌ Error parsing customer $i: $e');
            print('❌ Customer data: ${dataArray[i]}');
            rethrow;
          }
        }

        final pagination = Pagination.fromJson(paginationData);

        print('✅ Got ${customers.length} customers');
        return PaginatedCustomersResult(
          customers: customers,
          pagination: pagination,
        );
      } else {
        final Map<String, dynamic> errorData = jsonDecode(response.body);
        final errorInfo = ErrorInfo.fromJson(errorData['error'] ?? {});
        throw ApiException(
          statusCode: response.statusCode,
          error: errorInfo,
        );
      }
    } catch (e) {
      print('❌ Exception in getCustomers: $e');
      rethrow;
    }
  }
  
  // Search customers
  Future<List<Customer>> searchCustomers(String search, {int limit = 10}) async {
    print('🔍 Searching customers with query: $search');
    final queryParams = {
      'search': search,
      'limit': limit.toString(),
    };

    final response = await _apiService.get('/customers/search', queryParams: queryParams);
    print('🔍 Search response status: ${response.statusCode}');
    print('🔍 Search response body: ${response.body}');

    try {
      final result = _apiService.handleListResponse(
        response,
        (json) => Customer.fromJson(json),
      );
      print('✅ Search found ${result.length} customers');
      return result;
    } catch (e) {
      print('❌ Search error: $e');
      rethrow;
    }
  }
  
  // Get customer by ID
  Future<Customer> getCustomerById(int id) async {
    final response = await _apiService.get('/customers/$id');
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to get customer');
    }
  }
  
  // Get customer with statistics
  Future<CustomerWithStats> getCustomerWithStats(int id) async {
    print('📊 Getting customer stats for ID: $id');
    final response = await _apiService.get('/customers/$id/stats');

    print('📊 Customer stats response status: ${response.statusCode}');
    print('📊 Customer stats response body: ${response.body}');

    try {
      final customerResponse = _apiService.handleResponse(
        response,
        (data) => ApiResponse<CustomerWithStats>.fromJson(
          data,
          (json) => CustomerWithStats.fromJson(json as Map<String, dynamic>),
        ),
      );

      if (customerResponse.success && customerResponse.data != null) {
        print('✅ Customer stats loaded successfully');
        return customerResponse.data!;
      } else {
        print('❌ Customer stats failed: ${customerResponse.error?.message}');
        throw Exception(customerResponse.error?.message ?? 'Failed to get customer stats');
      }
    } catch (e) {
      print('❌ Exception in getCustomerWithStats: $e');
      rethrow;
    }
  }
  
  // Create customer
  Future<Customer> createCustomer(CreateCustomerRequest request) async {
    final response = await _apiService.post('/customers', body: request.toJson());
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to create customer');
    }
  }
  
  // Update customer
  Future<Customer> updateCustomer(int id, UpdateCustomerRequest request) async {
    final response = await _apiService.put('/customers/$id', body: request.toJson());
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to update customer');
    }
  }
  
  // Delete customer
  Future<void> deleteCustomer(int id) async {
    final response = await _apiService.delete('/customers/$id');
    
    final deleteResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<dynamic>.fromJson(data, (json) => json),
    );
    
    if (!deleteResponse.success) {
      throw Exception(deleteResponse.error?.message ?? 'Failed to delete customer');
    }
  }
}

class PaginatedCustomersResult {
  final List<Customer> customers;
  final Pagination pagination;
  
  PaginatedCustomersResult({
    required this.customers,
    required this.pagination,
  });
}
