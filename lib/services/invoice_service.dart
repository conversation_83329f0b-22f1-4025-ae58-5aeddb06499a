import '../models/invoice.dart';
import '../models/api_response.dart';
import 'api_service.dart';

class InvoiceService {
  final ApiService _apiService = ApiService.instance;
  
  static InvoiceService? _instance;
  static InvoiceService get instance => _instance ??= InvoiceService._();
  
  InvoiceService._();
  
  // Get customer invoices with pagination
  Future<PaginatedInvoicesResult> getCustomerInvoices(
    int customerId, {
    int page = 1,
    int perPage = 10,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'per_page': perPage.toString(),
    };
    
    final response = await _apiService.get(
      '/customers/$customerId/invoices',
      queryParams: queryParams,
    );
    
    print('📋 Getting customer invoices for ID: $customerId');
    print('📋 Response status: ${response.statusCode}');
    print('📋 Response body: ${response.body}');

    final responseData = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Map<String, dynamic>>.fromJson(
        data,
        (json) => json as Map<String, dynamic>,
      ),
    );

    if (responseData.success && responseData.data != null) {
      final responseBody = responseData.data!;
      final invoicesData = responseBody['invoices'] as List<dynamic>;
      final paginationData = responseBody['pagination'] as Map<String, dynamic>;

      final invoices = invoicesData
          .map((item) => Invoice.fromJson(item as Map<String, dynamic>))
          .toList();

      final pagination = Pagination.fromJson(paginationData);

      print('✅ Loaded ${invoices.length} invoices');
      return PaginatedInvoicesResult(
        invoices: invoices,
        pagination: pagination,
      );
    } else {
      print('❌ Failed to get customer invoices: ${responseData.error?.message}');
      throw Exception(responseData.error?.message ?? 'Failed to get invoices');
    }
  }
  
  // Search customer invoices
  Future<PaginatedInvoicesResult> searchCustomerInvoices(
    int customerId, {
    InvoiceStatus? status,
    String? payment,
    double? minTotal,
    double? maxTotal,
    String? search,
    int page = 1,
    int perPage = 10,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'per_page': perPage.toString(),
    };
    
    if (status != null) {
      queryParams['status'] = status.value;
    }
    if (payment != null && payment.isNotEmpty) {
      queryParams['payment'] = payment;
    }
    if (minTotal != null) {
      queryParams['min_total'] = minTotal.toString();
    }
    if (maxTotal != null) {
      queryParams['max_total'] = maxTotal.toString();
    }
    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }
    
    final response = await _apiService.get(
      '/customers/$customerId/invoices/search',
      queryParams: queryParams,
    );
    
    print('🔍 Searching customer invoices for ID: $customerId');
    print('🔍 Response status: ${response.statusCode}');
    print('🔍 Response body: ${response.body}');

    final responseData = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Map<String, dynamic>>.fromJson(
        data,
        (json) => json as Map<String, dynamic>,
      ),
    );

    if (responseData.success && responseData.data != null) {
      final responseBody = responseData.data!;
      final invoicesData = responseBody['invoices'] as List<dynamic>;
      final paginationData = responseBody['pagination'] as Map<String, dynamic>;

      final invoices = invoicesData
          .map((item) => Invoice.fromJson(item as Map<String, dynamic>))
          .toList();

      final pagination = Pagination.fromJson(paginationData);

      print('✅ Found ${invoices.length} invoices');
      return PaginatedInvoicesResult(
        invoices: invoices,
        pagination: pagination,
      );
    } else {
      print('❌ Failed to search invoices: ${responseData.error?.message}');
      throw Exception(responseData.error?.message ?? 'Failed to search invoices');
    }
  }
  
  // Get invoice statistics for customer
  Future<InvoiceStats> getInvoiceStats(int customerId) async {
    final response = await _apiService.get('/customers/$customerId/invoices/stats');
    
    final statsResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<InvoiceStats>.fromJson(
        data,
        (json) => InvoiceStats.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (statsResponse.success && statsResponse.data != null) {
      return statsResponse.data!;
    } else {
      throw Exception(statsResponse.error?.message ?? 'Failed to get invoice stats');
    }
  }
  
  // Get invoice by ID
  Future<Invoice> getInvoiceById(int id) async {
    final response = await _apiService.get('/invoices/$id');
    
    final invoiceResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Invoice>.fromJson(
        data,
        (json) => Invoice.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (invoiceResponse.success && invoiceResponse.data != null) {
      return invoiceResponse.data!;
    } else {
      throw Exception(invoiceResponse.error?.message ?? 'Failed to get invoice');
    }
  }
  
  // Create invoice
  Future<Invoice> createInvoice(CreateInvoiceRequest request) async {
    print('📋 Creating invoice with request: ${request.toJson()}');
    final response = await _apiService.post('/invoices', body: request.toJson());

    print('📋 Create invoice response status: ${response.statusCode}');
    print('📋 Create invoice response body: ${response.body}');

    try {
      final invoiceResponse = _apiService.handleResponse(
        response,
        (data) => ApiResponse<Invoice>.fromJson(
          data,
          (json) => Invoice.fromJson(json as Map<String, dynamic>),
        ),
      );

      if (invoiceResponse.success && invoiceResponse.data != null) {
        print('✅ Invoice created successfully: ${invoiceResponse.data!.id}');
        return invoiceResponse.data!;
      } else {
        print('❌ Invoice creation failed: ${invoiceResponse.error?.message}');
        throw Exception(invoiceResponse.error?.message ?? 'Failed to create invoice');
      }
    } catch (e) {
      print('❌ Exception in createInvoice: $e');
      rethrow;
    }
  }
  
  // Update invoice
  Future<Invoice> updateInvoice(int id, UpdateInvoiceRequest request) async {
    final response = await _apiService.put('/invoices/$id', body: request.toJson());
    
    final invoiceResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Invoice>.fromJson(
        data,
        (json) => Invoice.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (invoiceResponse.success && invoiceResponse.data != null) {
      return invoiceResponse.data!;
    } else {
      throw Exception(invoiceResponse.error?.message ?? 'Failed to update invoice');
    }
  }
  
  // Delete invoice
  Future<void> deleteInvoice(int id) async {
    final response = await _apiService.delete('/invoices/$id');
    
    final deleteResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<dynamic>.fromJson(data, (json) => json),
    );
    
    if (!deleteResponse.success) {
      throw Exception(deleteResponse.error?.message ?? 'Failed to delete invoice');
    }
  }
}

class PaginatedInvoicesResult {
  final List<Invoice> invoices;
  final Pagination pagination;
  
  PaginatedInvoicesResult({
    required this.invoices,
    required this.pagination,
  });
}
