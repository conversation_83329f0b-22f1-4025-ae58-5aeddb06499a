import '../models/api_response.dart';
import 'api_service.dart';

class AuthService {
  final ApiService _apiService = ApiService.instance;
  
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();
  
  // Login user
  Future<LoginResponse> login(String username, String password) async {
    final request = LoginRequest(username: username, password: password);
    
    final response = await _apiService.post(
      '/auth/login',
      body: request.toJson(),
      includeAuth: false,
    );
    
    final loginResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<LoginResponse>.fromJson(
        data,
        (json) => LoginResponse.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (loginResponse.success && loginResponse.data != null) {
      // Save token
      await _apiService.saveAuthToken(loginResponse.data!.token);
      return loginResponse.data!;
    } else {
      throw Exception(loginResponse.error?.message ?? 'Login failed');
    }
  }
  
  // Get current user profile
  Future<UserProfile> getProfile() async {
    final response = await _apiService.get('/auth/me');
    
    final profileResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<UserProfile>.fromJson(
        data,
        (json) => UserProfile.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (profileResponse.success && profileResponse.data != null) {
      return profileResponse.data!;
    } else {
      throw Exception(profileResponse.error?.message ?? 'Failed to get profile');
    }
  }
  
  // Logout user
  Future<void> logout() async {
    await _apiService.clearAuthToken();
  }
  
  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await _apiService.getAuthToken();
    return token != null && token.isNotEmpty;
  }
}
