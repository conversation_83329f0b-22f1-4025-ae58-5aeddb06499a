import 'package:json_annotation/json_annotation.dart';

part 'customer.g.dart';

// Custom converter for string to double
class StringToDoubleConverter implements JsonConverter<double, dynamic> {
  const StringToDoubleConverter();

  @override
  double fromJson(dynamic json) {
    if (json is String) {
      return double.parse(json);
    } else if (json is num) {
      return json.toDouble();
    }
    throw ArgumentError('Cannot convert $json to double');
  }

  @override
  dynamic toJson(double object) => object.toString();
}

@JsonSerializable()
class Customer {
  final int id;
  final String name;
  final String address;
  final String phone;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @J<PERSON><PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;

  Customer({
    required this.id,
    required this.name,
    required this.address,
    required this.phone,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => _$CustomerFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerToJson(this);
}

@JsonSerializable()
class CreateCustomerRequest {
  final String name;
  final String? address;
  final String? phone;

  CreateCustomerRequest({
    required this.name,
    this.address,
    this.phone,
  });

  factory CreateCustomerRequest.fromJson(Map<String, dynamic> json) => 
      _$CreateCustomerRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateCustomerRequestToJson(this);
}

@JsonSerializable()
class UpdateCustomerRequest {
  final String? name;
  final String? address;
  final String? phone;

  UpdateCustomerRequest({
    this.name,
    this.address,
    this.phone,
  });

  factory UpdateCustomerRequest.fromJson(Map<String, dynamic> json) => 
      _$UpdateCustomerRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateCustomerRequestToJson(this);
}

@JsonSerializable()
class CustomerWithStats {
  final int id;
  final String name;
  final String address;
  final String phone;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @JsonKey(name: 'invoice_count')
  final int invoiceCount;
  @JsonKey(name: 'total_amount')
  @StringToDoubleConverter()
  final double totalAmount;
  @JsonKey(name: 'paid_amount')
  @StringToDoubleConverter()
  final double paidAmount;
  @JsonKey(name: 'pending_amount')
  @StringToDoubleConverter()
  final double pendingAmount;

  CustomerWithStats({
    required this.id,
    required this.name,
    required this.address,
    required this.phone,
    required this.createdAt,
    required this.updatedAt,
    required this.invoiceCount,
    required this.totalAmount,
    required this.paidAmount,
    required this.pendingAmount,
  });

  factory CustomerWithStats.fromJson(Map<String, dynamic> json) => 
      _$CustomerWithStatsFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerWithStatsToJson(this);
}
