// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Invoice _$InvoiceFromJson(Map<String, dynamic> json) => Invoice(
      id: (json['id'] as num).toInt(),
      total: (json['total'] as num).toDouble(),
      payment: json['payment'] as String,
      status: $enumDecode(_$InvoiceStatusEnumMap, json['status']),
      created: DateTime.parse(json['created'] as String),
      customerId: (json['customer_id'] as num).toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      customer: json['customer'] == null
          ? null
          : Customer.fromJson(json['customer'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$InvoiceToJson(Invoice instance) => <String, dynamic>{
      'id': instance.id,
      'total': instance.total,
      'payment': instance.payment,
      'status': _$InvoiceStatusEnumMap[instance.status]!,
      'created': instance.created.toIso8601String(),
      'customer_id': instance.customerId,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'customer': instance.customer,
    };

const _$InvoiceStatusEnumMap = {
  InvoiceStatus.sent: 'Sent',
  InvoiceStatus.paid: 'Paid',
  InvoiceStatus.canceled: 'Canceled',
};

CreateInvoiceRequest _$CreateInvoiceRequestFromJson(
        Map<String, dynamic> json) =>
    CreateInvoiceRequest(
      total: (json['total'] as num).toDouble(),
      payment: json['payment'] as String,
      status: $enumDecode(_$InvoiceStatusEnumMap, json['status']),
      created: json['created'] == null
          ? null
          : DateTime.parse(json['created'] as String),
      customerId: (json['customer_id'] as num).toInt(),
    );

Map<String, dynamic> _$CreateInvoiceRequestToJson(
        CreateInvoiceRequest instance) =>
    <String, dynamic>{
      'total': instance.total,
      'payment': instance.payment,
      'status': _$InvoiceStatusEnumMap[instance.status]!,
      'created': instance.created?.toIso8601String(),
      'customer_id': instance.customerId,
    };

UpdateInvoiceRequest _$UpdateInvoiceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateInvoiceRequest(
      total: (json['total'] as num?)?.toDouble(),
      payment: json['payment'] as String?,
      status: $enumDecodeNullable(_$InvoiceStatusEnumMap, json['status']),
      created: json['created'] == null
          ? null
          : DateTime.parse(json['created'] as String),
    );

Map<String, dynamic> _$UpdateInvoiceRequestToJson(
        UpdateInvoiceRequest instance) =>
    <String, dynamic>{
      'total': instance.total,
      'payment': instance.payment,
      'status': _$InvoiceStatusEnumMap[instance.status],
      'created': instance.created?.toIso8601String(),
    };

InvoiceStats _$InvoiceStatsFromJson(Map<String, dynamic> json) => InvoiceStats(
      totalCount: (json['total_count'] as num).toInt(),
      totalAmount:
          const StringToDoubleConverter().fromJson(json['total_amount']),
      paidCount: (json['paid_count'] as num).toInt(),
      paidAmount: const StringToDoubleConverter().fromJson(json['paid_amount']),
      sentCount: (json['sent_count'] as num).toInt(),
      sentAmount: const StringToDoubleConverter().fromJson(json['sent_amount']),
      canceledCount: (json['canceled_count'] as num).toInt(),
      canceledAmount:
          const StringToDoubleConverter().fromJson(json['canceled_amount']),
    );

Map<String, dynamic> _$InvoiceStatsToJson(InvoiceStats instance) =>
    <String, dynamic>{
      'total_count': instance.totalCount,
      'total_amount':
          const StringToDoubleConverter().toJson(instance.totalAmount),
      'paid_count': instance.paidCount,
      'paid_amount':
          const StringToDoubleConverter().toJson(instance.paidAmount),
      'sent_count': instance.sentCount,
      'sent_amount':
          const StringToDoubleConverter().toJson(instance.sentAmount),
      'canceled_count': instance.canceledCount,
      'canceled_amount':
          const StringToDoubleConverter().toJson(instance.canceledAmount),
    };
