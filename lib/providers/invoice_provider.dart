import 'package:flutter/foundation.dart';
import '../models/invoice.dart';
import '../models/api_response.dart';
import '../services/invoice_service.dart';

class InvoiceProvider extends ChangeNotifier {
  final InvoiceService _invoiceService = InvoiceService.instance;
  
  List<Invoice> _invoices = [];
  Pagination? _pagination;
  InvoiceStats? _stats;
  bool _isLoading = false;
  bool _isLoadingStats = false;
  String? _error;
  int? _currentCustomerId;
  
  // Filter state
  InvoiceStatus? _statusFilter;
  String? _paymentFilter;
  double? _minTotalFilter;
  double? _maxTotalFilter;
  String? _searchFilter;
  
  List<Invoice> get invoices => _invoices;
  Pagination? get pagination => _pagination;
  InvoiceStats? get stats => _stats;
  bool get isLoading => _isLoading;
  bool get isLoadingStats => _isLoadingStats;
  String? get error => _error;
  int? get currentCustomerId => _currentCustomerId;
  
  // Filter getters
  InvoiceStatus? get statusFilter => _statusFilter;
  String? get paymentFilter => _paymentFilter;
  double? get minTotalFilter => _minTotalFilter;
  double? get maxTotalFilter => _maxTotalFilter;
  String? get searchFilter => _searchFilter;
  
  // Load customer invoices
  Future<void> loadCustomerInvoices(
    int customerId, {
    int page = 1,
    int perPage = 10,
    bool append = false,
  }) async {
    if (!append) {
      _setLoading(true);
    }
    _clearError();
    _currentCustomerId = customerId;
    
    try {
      final result = await _invoiceService.getCustomerInvoices(
        customerId,
        page: page,
        perPage: perPage,
      );
      
      if (append) {
        _invoices.addAll(result.invoices);
      } else {
        _invoices = result.invoices;
      }
      
      _pagination = result.pagination;
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }
  
  // Search customer invoices with filters
  Future<void> searchCustomerInvoices(
    int customerId, {
    InvoiceStatus? status,
    String? payment,
    double? minTotal,
    double? maxTotal,
    String? search,
    int page = 1,
    int perPage = 10,
    bool append = false,
  }) async {
    if (!append) {
      _setLoading(true);
    }
    _clearError();
    _currentCustomerId = customerId;
    
    // Update filter state
    _statusFilter = status;
    _paymentFilter = payment;
    _minTotalFilter = minTotal;
    _maxTotalFilter = maxTotal;
    _searchFilter = search;
    
    try {
      final result = await _invoiceService.searchCustomerInvoices(
        customerId,
        status: status,
        payment: payment,
        minTotal: minTotal,
        maxTotal: maxTotal,
        search: search,
        page: page,
        perPage: perPage,
      );
      
      if (append) {
        _invoices.addAll(result.invoices);
      } else {
        _invoices = result.invoices;
      }
      
      _pagination = result.pagination;
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }
  
  // Load invoice statistics
  Future<void> loadInvoiceStats(int customerId) async {
    _setLoadingStats(true);
    _clearError();
    
    try {
      _stats = await _invoiceService.getInvoiceStats(customerId);
      _setLoadingStats(false);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      _setLoadingStats(false);
    }
  }
  
  // Create invoice
  Future<bool> createInvoice(CreateInvoiceRequest request) async {
    print('📋 InvoiceProvider.createInvoice called');
    print('📋 Request: ${request.toJson()}');
    _setLoading(true);
    _clearError();

    try {
      print('⏳ Calling invoice service...');
      final invoice = await _invoiceService.createInvoice(request);
      print('✅ Invoice created: ${invoice.id}');
      _invoices.insert(0, invoice);

      // Refresh stats if we have them
      if (_stats != null && _currentCustomerId != null) {
        await loadInvoiceStats(_currentCustomerId!);
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      print('❌ Invoice creation failed: $e');
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }
  
  // Update invoice
  Future<bool> updateInvoice(int id, UpdateInvoiceRequest request) async {
    _setLoading(true);
    _clearError();
    
    try {
      final updatedInvoice = await _invoiceService.updateInvoice(id, request);
      
      // Update in list
      final index = _invoices.indexWhere((i) => i.id == id);
      if (index != -1) {
        _invoices[index] = updatedInvoice;
      }
      
      // Refresh stats if we have them
      if (_stats != null && _currentCustomerId != null) {
        await loadInvoiceStats(_currentCustomerId!);
      }
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }
  
  // Delete invoice
  Future<bool> deleteInvoice(int id) async {
    _setLoading(true);
    _clearError();
    
    try {
      await _invoiceService.deleteInvoice(id);
      
      // Remove from list
      _invoices.removeWhere((i) => i.id == id);
      
      // Refresh stats if we have them
      if (_stats != null && _currentCustomerId != null) {
        await loadInvoiceStats(_currentCustomerId!);
      }
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }
  
  // Load more invoices (pagination)
  Future<void> loadMoreInvoices() async {
    if (_pagination != null && 
        _pagination!.page < _pagination!.totalPages && 
        _currentCustomerId != null) {
      
      if (hasActiveFilters()) {
        await searchCustomerInvoices(
          _currentCustomerId!,
          status: _statusFilter,
          payment: _paymentFilter,
          minTotal: _minTotalFilter,
          maxTotal: _maxTotalFilter,
          search: _searchFilter,
          page: _pagination!.page + 1,
          perPage: _pagination!.perPage,
          append: true,
        );
      } else {
        await loadCustomerInvoices(
          _currentCustomerId!,
          page: _pagination!.page + 1,
          perPage: _pagination!.perPage,
          append: true,
        );
      }
    }
  }
  
  // Clear filters
  void clearFilters() {
    _statusFilter = null;
    _paymentFilter = null;
    _minTotalFilter = null;
    _maxTotalFilter = null;
    _searchFilter = null;
    
    if (_currentCustomerId != null) {
      loadCustomerInvoices(_currentCustomerId!);
    }
  }
  
  // Check if there are active filters
  bool hasActiveFilters() {
    return _statusFilter != null ||
           (_paymentFilter != null && _paymentFilter!.isNotEmpty) ||
           _minTotalFilter != null ||
           _maxTotalFilter != null ||
           (_searchFilter != null && _searchFilter!.isNotEmpty);
  }
  
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setLoadingStats(bool loading) {
    _isLoadingStats = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }
  
  void _clearError() {
    _error = null;
  }
  
  void clearInvoices() {
    _invoices.clear();
    _pagination = null;
    _stats = null;
    _currentCustomerId = null;
    _clearFilters();
    notifyListeners();
  }
  
  void _clearFilters() {
    _statusFilter = null;
    _paymentFilter = null;
    _minTotalFilter = null;
    _maxTotalFilter = null;
    _searchFilter = null;
  }
}
