import 'package:flutter/foundation.dart';
import '../models/customer.dart';
import '../models/api_response.dart';
import '../services/customer_service.dart';

class CustomerProvider extends ChangeNotifier {
  final CustomerService _customerService = CustomerService.instance;
  
  List<Customer> _customers = [];
  Pagination? _pagination;
  Customer? _selectedCustomer;
  CustomerWithStats? _selectedCustomerStats;
  bool _isLoading = false;
  bool _isLoadingStats = false;
  String? _error;
  String _searchQuery = '';
  
  List<Customer> get customers => _customers;
  Pagination? get pagination => _pagination;
  Customer? get selectedCustomer => _selectedCustomer;
  CustomerWithStats? get selectedCustomerStats => _selectedCustomerStats;
  bool get isLoading => _isLoading;
  bool get isLoadingStats => _isLoadingStats;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  
  // Load customers
  Future<void> loadCustomers({
    int page = 1,
    int perPage = 10,
    String? search,
    bool append = false,
  }) async {
    if (!append) {
      _setLoading(true);
    }
    _clearError();
    
    try {
      final result = await _customerService.getCustomers(
        page: page,
        perPage: perPage,
        search: search,
      );
      
      if (append) {
        _customers.addAll(result.customers);
      } else {
        _customers = result.customers;
      }
      
      _pagination = result.pagination;
      _searchQuery = search ?? '';
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }
  
  // Search customers
  Future<List<Customer>> searchCustomers(String query) async {
    try {
      return await _customerService.searchCustomers(query);
    } catch (e) {
      _setError(e.toString());
      return [];
    }
  }
  
  // Select customer
  void selectCustomer(Customer customer) {
    _selectedCustomer = customer;
    _selectedCustomerStats = null;
    notifyListeners();
  }
  
  // Load customer with stats
  Future<void> loadCustomerStats(int customerId) async {
    _setLoadingStats(true);
    _clearError();
    
    try {
      _selectedCustomerStats = await _customerService.getCustomerWithStats(customerId);
      _setLoadingStats(false);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      _setLoadingStats(false);
    }
  }
  
  // Create customer
  Future<bool> createCustomer(CreateCustomerRequest request) async {
    _setLoading(true);
    _clearError();
    
    try {
      final customer = await _customerService.createCustomer(request);
      _customers.insert(0, customer);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }
  
  // Update customer
  Future<bool> updateCustomer(int id, UpdateCustomerRequest request) async {
    _setLoading(true);
    _clearError();
    
    try {
      final updatedCustomer = await _customerService.updateCustomer(id, request);
      
      // Update in list
      final index = _customers.indexWhere((c) => c.id == id);
      if (index != -1) {
        _customers[index] = updatedCustomer;
      }
      
      // Update selected customer if it's the same
      if (_selectedCustomer?.id == id) {
        _selectedCustomer = updatedCustomer;
      }
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }
  
  // Delete customer
  Future<bool> deleteCustomer(int id) async {
    _setLoading(true);
    _clearError();
    
    try {
      await _customerService.deleteCustomer(id);
      
      // Remove from list
      _customers.removeWhere((c) => c.id == id);
      
      // Clear selected customer if it's the same
      if (_selectedCustomer?.id == id) {
        _selectedCustomer = null;
        _selectedCustomerStats = null;
      }
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }
  
  // Load more customers (pagination)
  Future<void> loadMoreCustomers() async {
    if (_pagination != null && _pagination!.page < _pagination!.totalPages) {
      await loadCustomers(
        page: _pagination!.page + 1,
        perPage: _pagination!.perPage,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
        append: true,
      );
    }
  }
  
  // Refresh customers
  Future<void> refreshCustomers() async {
    await loadCustomers(
      search: _searchQuery.isNotEmpty ? _searchQuery : null,
    );
  }
  
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setLoadingStats(bool loading) {
    _isLoadingStats = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }
  
  void _clearError() {
    _error = null;
  }
  
  void clearSelectedCustomer() {
    _selectedCustomer = null;
    _selectedCustomerStats = null;
    notifyListeners();
  }
}
