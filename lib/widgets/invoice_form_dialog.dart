import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../providers/invoice_provider.dart';

class InvoiceFormDialog extends StatefulWidget {
  final int customerId;
  final Invoice? invoice;
  final Function(dynamic) onSave;

  const InvoiceFormDialog({
    super.key,
    required this.customerId,
    this.invoice,
    required this.onSave,
  });

  @override
  State<InvoiceFormDialog> createState() => _InvoiceFormDialogState();
}

class _InvoiceFormDialogState extends State<InvoiceFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _totalController = TextEditingController();
  final _paymentController = TextEditingController();
  
  InvoiceStatus _selectedStatus = InvoiceStatus.sent;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    if (widget.invoice != null) {
      _totalController.text = widget.invoice!.total.toString();
      _paymentController.text = widget.invoice!.payment;
      _selectedStatus = widget.invoice!.status;
      _selectedDate = widget.invoice!.created;
    }
  }

  @override
  void dispose() {
    _totalController.dispose();
    _paymentController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  void _save() {
    if (_formKey.currentState!.validate()) {
      final total = double.parse(_totalController.text);
      
      final request = widget.invoice == null
          ? CreateInvoiceRequest(
              total: total,
              payment: _paymentController.text.trim(),
              status: _selectedStatus,
              created: _selectedDate,
              customerId: widget.customerId,
            )
          : UpdateInvoiceRequest(
              total: total,
              payment: _paymentController.text.trim(),
              status: _selectedStatus,
              created: _selectedDate,
            );

      widget.onSave(request);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.invoice != null;

    return AlertDialog(
      title: Text(isEditing ? 'Edit Invoice' : 'Add Invoice'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Total field
              TextFormField(
                controller: _totalController,
                decoration: const InputDecoration(
                  labelText: 'Total Amount *',
                  prefixText: '\$ ',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter total amount';
                  }
                  final total = double.tryParse(value);
                  if (total == null || total <= 0) {
                    return 'Please enter a valid amount';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Payment method field
              TextFormField(
                controller: _paymentController,
                decoration: const InputDecoration(
                  labelText: 'Payment Method *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter payment method';
                  }
                  if (value.trim().length > 50) {
                    return 'Payment method must be less than 50 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Status dropdown
              DropdownButtonFormField<InvoiceStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status *',
                  border: OutlineInputBorder(),
                ),
                items: InvoiceStatus.values.map((status) {
                  Color color;
                  IconData icon;
                  
                  switch (status) {
                    case InvoiceStatus.paid:
                      color = Colors.green;
                      icon = Icons.check_circle;
                      break;
                    case InvoiceStatus.sent:
                      color = Colors.orange;
                      icon = Icons.pending;
                      break;
                    case InvoiceStatus.canceled:
                      color = Colors.red;
                      icon = Icons.cancel;
                      break;
                  }
                  
                  return DropdownMenuItem(
                    value: status,
                    child: Row(
                      children: [
                        Icon(icon, color: color, size: 16),
                        const SizedBox(width: 8),
                        Text(status.displayName),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (status) {
                  if (status != null) {
                    setState(() {
                      _selectedStatus = status;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // Date field
              InkWell(
                onTap: _selectDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Invoice Date *',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    DateFormat('MMM dd, yyyy').format(_selectedDate),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        Consumer<InvoiceProvider>(
          builder: (context, invoiceProvider, child) {
            return ElevatedButton(
              onPressed: invoiceProvider.isLoading ? null : _save,
              child: invoiceProvider.isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(isEditing ? 'Update' : 'Create'),
            );
          },
        ),
      ],
    );
  }
}
